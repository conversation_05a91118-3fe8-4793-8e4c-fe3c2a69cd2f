from fastapi import <PERSON>AP<PERSON>, HTTPException, Request, Path, Query, Body, Depends, Security
from fastapi.security.api_key import <PERSON>KeyHeader
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
from typing import Optional, Dict, List, Union, Any
from pydantic import BaseModel, Field
import os
from ping3 import ping
from dotenv import load_dotenv

# Load environment variables from .env file
# This ensures the API key is always loaded from .env regardless of execution context
load_dotenv()

# API key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=True)

# Verify API key is loaded (for debugging)
gandalf_api_key = os.getenv("GANDALF_API_KEY")
if not gandalf_api_key:
    print("WARNING: GANDALF_API_KEY not found in environment variables!")
else:
    print(f"INFO: GANDALF_API_KEY loaded successfully (length: {len(gandalf_api_key)} characters)")

# API key validation dependency
async def get_api_key(api_key: str = Security(api_key_header)):
    # Get the API key from environment (should be loaded from .env)
    gandalf_api_key = os.getenv("GANDALF_API_KEY", "")

    if not gandalf_api_key:
        raise HTTPException(
            status_code=500,
            detail="Server configuration error: API key not configured"
        )

    # Support multiple API keys separated by commas
    api_keys = [key.strip() for key in gandalf_api_key.split(",") if key.strip()]

    if not api_keys or api_key not in api_keys:
        raise HTTPException(
            status_code=403,
            detail="Invalid or missing API key"
        )
    return api_key

class SaveQueryRequest(BaseModel):
    query: str
    params: Optional[tuple] = None
    return_new_id: bool = False

from mtikClass import mtikClass
from SQLiteClass import SQLiteClass
from logger_config import logger
import datetime

app = FastAPI(
    title="Gandalf API",
    description="API for managing network rentals and router configurations",
    version="1.0.0",
    dependencies=[Depends(get_api_key)],  # Apply API key validation to all routes
    contact={
        "name": "Eastern Shore Communications",
        "url": "https://esvc.us",
    },
)
mtik = mtikClass()
db = SQLiteClass()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Request failed: {request.url.path} - Error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": str(e)}
)

# Pydantic models with enhanced documentation
class NetworkOrder(BaseModel):
    unit: str = Field(..., description="Unit identifier", example="201")
    password: str = Field(..., description="Network password", example="SecurePass123")
    days: int = Field(..., description="Duration in days", example=30)
    package: str = Field(..., description="Network package type", example="250/50")
    email: str = Field(..., description="Customer email", example="<EMAIL>")
    price: float = Field(..., description="Package price", example=49.99)
    hard_expiration_date: Optional[str] = Field(None, description="Hard expiration date (YYYY-MM-DD)", example="2024-12-31")
    isTest: Optional[int] = Field(0, description="Flag to categorize order as test environment", example=0)
    origin: Optional[str] = Field(None, description="Server processing this rental", example="gandalf-prod-1")
    create_net: Optional[bool] = Field(False, description="Whether to create network immediately", example=False)

    class Config:
        schema_extra = {
            "example": {
                "unit": "201",
                "password": "SecurePass123",
                "days": 4,
                "package": "250/50",
                "email": "<EMAIL>",
                "price": 49.99,
                "hard_expiration_date": "2024-12-31",
                "isTest": 0,
                "origin": "gandalf-prod-1",
                "create_net": False
            }
        }

class OrderIdOnly(BaseModel):
    order_id: int = Field(..., description="Order ID to look up or process")

class OrderUpdate(BaseModel):
    transaction_id: str = Field(..., description="Payment transaction ID")
    order_id: int = Field(..., description="Order identifier")
    receipt_url: str = Field(..., description="URL to payment receipt")
    connect_net: bool = Field(False, description="Whether to connect the network immediately")

class QueueConfig(BaseModel):
    name: str = Field(..., description="Queue name")
    max_limit: str = Field(..., description="Maximum bandwidth limit")
    target: str = Field(..., description="Target network")
    queue: str = Field(..., description="Queue type")
    burst_limit: str = Field(..., description="Burst bandwidth limit")
    burst_threshold: str = Field(..., description="Burst threshold")
    dst: str = Field(..., description="Destination interface")
    burst_time: str = Field(..., description="Burst duration")

# Add these Pydantic models for request/response validation
class RouterInfo(BaseModel):
    hostname: str
    model: str
    firmware: str
    rental: bool
    ssid_24: str
    freq_24: Union[int, str]  # Allow string values like 'auto'
    freq_5: Union[int, str]   # Allow string values like 'auto'
    ssid_5: str
    ssid_pwd: str
    ssid_clients: int
    queue_name: str
    ipaddress: str

class SSIDInfo(BaseModel):
    ssid: str
    password: str
    master_interface: str
    security_profile: str
    mac_address: str
    band: str
    channel_width: str
    frequency: str

class RentalOrderInfo(BaseModel):
    order_id: int
    et_days: int
    package: str
    created_at: str
    hard_expiration_date: Optional[str]
    isTest: int

class SecurityProfileResponse(BaseModel):
    exists: bool
    rental_order_info: Optional[List[RentalOrderInfo]] = None

# SQLiteClass endpoints
@app.get("/unit/{unit}/router-ip",
    response_model=str,
    tags=["Units"],
    summary="Get Router IP by Unit",
    description="Retrieves the IP address of the router associated with a specific unit",
    responses={
        200: {"description": "Successfully retrieved router IP"},
        404: {"description": "Unit not found"},
        500: {"description": "Internal server error"}
    })
async def get_unit_router_ip(
    unit: str = Path(..., description="Unit identifier (without 'et' prefix)")
):
    """
    Get the IP address for a specific unit's router.

    Args:
        unit: Unit identifier (without 'et' prefix)

    Returns:
        String containing the router's IP address

    Raises:
        HTTPException: If retrieval fails or unit not found
    """
    try:
        ip_address = db.get_unit_router_ip(unit)
        if ip_address is None:
            raise HTTPException(
                status_code=404,
                detail=f"No router IP found for unit {unit}"
            )
        return ip_address
    except Exception as e:
        logger.error(f"Error getting router IP for unit {unit}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve router IP: {str(e)}"
        )

@app.post("/order/update")
async def update_order(order: OrderUpdate):
    result = db.update_order(order.order_id, order.transaction_id, order.receipt_url, order.connect_net)
    if not result:
        raise HTTPException(status_code=400, detail=result[1])
    return result

@app.post("/network/new",
    response_model=Dict[str, Union[int, str]],
    tags=["Networks"],
    summary="Create New Network",
    description="Creates a new network rental order with specified parameters.")
async def new_network(network: NetworkOrder):
    # Look up router IP for the unit
    if network.create_net:
        ip_address = db.get_unit_router_ip(network.unit)
        if not ip_address:
            raise HTTPException(status_code=404, detail="Router IP not found for unit")
        success, message = mtik.new_rental(ip_address, network.unit, network.password, network.package)
        if not success:
            raise HTTPException(status_code=400, detail=f"Network activation failed: {message}")

    # Proceed to create the order in the DB as usual
    result, message = db.new_network(
        network.unit, network.password, network.days, network.package, network.email,
        network.price, network.hard_expiration_date, network.isTest, network.origin
    )
    if not result:
        raise HTTPException(status_code=400, detail=message)
    return {"order_id": result, "message": message}
@app.get("/orders/month-to-date")
async def get_orders_mtd():
    # Remove the "orders" wrapper
    return db.get_orders_mtd()

@app.get("/orders/recent")
async def get_recent_order_info():
    # Remove the "orders" wrapper
    return db.get_recent_order_info()

@app.get("/orders/count")
async def orders_count():
    """
    Returns the count of orders in the database.
    """
    count = db.get_orders_count()
    return {"orders_count": count}


@app.get("/networks/active")
async def get_active_networks():
    # Remove the "networks" wrapper
    return db.get_active_networks()

@app.get("/routers")
async def get_routers():
    # Remove the "routers" wrapper
    return db.get_routers()

@app.get("/unit/{unit}/orders")
async def get_unit_orders(unit: str):
    # Remove the "orders" wrapper
    return db.get_unit_orders(unit)

# mtikClass endpoints


@app.post("/router/{ip}/reset")
async def reset_access_point(ip: str):
    result = mtik.reset_ap(ip)
    if isinstance(result, str) and "Error" in result:
        raise HTTPException(status_code=400, detail=result)
    return {"actions": result}

@app.post("/router/{ip}/queue")
async def add_queue(ip: str, queue: QueueConfig):
    result = mtik.add_queue(ip, queue.dict())
    if not result:
        raise HTTPException(status_code=400, detail="Failed to add queue")
    return {"status": "success"}

@app.get("/router/{ip}/queue")
async def get_queue_info(ip: str):
    result = mtik.get_queue(ip)
    if isinstance(result, str):
        raise HTTPException(status_code=400, detail=result)
    return result

@app.get("/router/{ip}/ssid")
async def get_ssid_info(ip: str):
    result = mtik.get_ssid(ip)
    if isinstance(result, str):
        raise HTTPException(status_code=400, detail=result)
    return result

@app.get("/router/{ip}/registrations",
    tags=["Routers"],
    summary="Get Registration List",
    description="Retrieves the wireless registration table from a router")
async def get_registration_list(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_registration_list(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Add these endpoints
@app.get("/router/{ip}/info",
    tags=["Routers"],
    summary="Get Router Information",
    response_model=RouterInfo,
    description="Retrieves detailed information about a router including wireless settings and queue configuration")
async def get_router_info(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_router_info(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/router/{ip}/ssid",
    tags=["Routers"],
    summary="Get SSID Information",
    response_model=List[SSIDInfo],
    description="Retrieves SSID configuration details for a router")
async def get_ssid(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_ssid(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/router/{ip}/registrations",
    tags=["Routers"],
    summary="Get Registration List",
    description="Retrieves the wireless registration table from a router")
async def get_registration_list(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_registration_list(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/router/{ip}/queue",
    tags=["Routers"],
    summary="Get Queue Configuration",
    description="Retrieves the queue configuration from a router")
async def get_queue(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_queue(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/router/{ip}/queue",
    tags=["Routers"],
    summary="Add Queue Configuration",
    description="Adds or updates queue configuration on a router")
async def add_queue(
    ip: str = Path(..., description="Router IP address"),
    queue: QueueConfig = Body(..., description="Queue configuration")
):
    try:
        result = mtik.add_queue(ip, queue.dict())
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return {"status": "success", "message": "Queue configuration updated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/router/{ip}/rental/new",
    tags=["Routers"],
    summary="Create New Rental",
    description="Creates a new rental configuration on a router")
async def new_rental(
    ip: str = Path(..., description="Router IP address"),
    unit: str = Query(..., description="Unit identifier"),
    password: str = Query(..., description="Rental password") ,
    package: str = Query(..., description="Package type (standard/streaming)")
):
    try:
        success, message = mtik.new_rental(ip, unit, password, package)
        if not success:
            raise HTTPException(status_code=400, detail=message)
        return {"status": "success", "message": message}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/router/{ip}/reset",
    tags=["Routers"],
    summary="Reset Router",
    description="Resets router configuration and removes rental settings")
async def reset_ap(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.reset_ap(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return {"actions": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/router/{ip}/hostname",
    tags=["Routers"],
    summary="Get Router Hostname",
    description="Retrieves the hostname of a router")
async def get_router_hostname(ip: str = Path(..., description="Router IP address")):
    try:
        result = mtik.get_router_hostname(ip)
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)
        return {"hostname": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/nas/{hostname}/ip/{ipaddress}",
    tags=["NAS"],
    summary="Update NAS IP Address",
    description="Updates the IP address for a NAS device with the given hostname",
    responses={
        200: {"description": "Successfully updated NAS IP address"},
        404: {"description": "NAS hostname not found"},
        500: {"description": "Database error"}
    })
async def update_nas(
    hostname: str = Path(..., description="NAS hostname (e.g., 'et708')"),
    ipaddress: str = Path(..., description="New IP address for the NAS device")
):
    try:
        result = db.update_nas(hostname, ipaddress)
        if result == 0:
            raise HTTPException(status_code=404, detail=f"NAS with hostname '{hostname}' not found")
        return {"status": "success", "message": f"Updated IP address for {hostname} to {ipaddress}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/router/{ipaddress}/rental/off",
    tags=["Routers"],
    summary="Turn Off Router Rental",
    description="Disables the rental status for a router with the specified IP address",
    responses={
        200: {"description": "Successfully turned off rental"},
        400: {"description": "Invalid IP address"},
        404: {"description": "Router not found"},
        500: {"description": "Database error"}
    })
async def router_turn_rental_off(
    ipaddress: str = Path(..., description="Router IP address")
):
    """
    Turns off rental status for a specific router.

    Args:
        ipaddress: Router IP address

    Returns:
        Dict containing success status and affected rows count
    """
    try:
        if not ipaddress:
            raise HTTPException(status_code=400, detail="IP address is required")

        result = db.router_turn_rental_off(ipaddress)

        if result == 0:
            raise HTTPException(status_code=404, detail=f"Router with IP '{ipaddress}' not found")

        return {
            "status": "success",
            "message": f"Rental turned off for router {ipaddress}",
            "affected_rows": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/router/save",
    tags=["Routers"],
    summary="Save Router Information",
    description="Saves or updates router information in the databases",
    response_model=Dict[str, str])
async def save_router(router: RouterInfo):
    """
    Save or update router information in the database.

    Args:
        router: Router information including hostname, model, firmware, etc.

    Returns:
        Dict containing success status and message
    """
    try:
        result = db.save_router(router.model_dump())
        if result == 0:
            raise HTTPException(
                status_code=400,
                detail="Failed to save router information"
            )
        return {
            "status": "success",
            "message": f"Router information saved successfully",
            "affected_rows": result
        }
    except Exception as e:
        logger.error(f"Error saving router information: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to save router information: {str(e)}"
        )

@app.get("/ping",
    tags=["Utility"],
    summary="Health check",
    description="Simple endpoint to verify API is running",
    response_model=Dict[str, str],
    responses={
        200: {"description": "API is running"},
    })
async def ping():
    """
    Basic health check endpoint.

    Returns:
        Dict with status indicating API is operational
    """
    return {"status": "ok", "timestamp": datetime.datetime.now().isoformat()}

@app.get("/unit/{unit}/security-profile-exists",
    tags=["Router"],
    summary="Check if security profile exists",
    description="Checks if a security profile exists on the specified router and returns rental order information if it exists",
    response_model=SecurityProfileResponse)
async def security_profile_exists(unit: str):
    """
    Check if security profile exists on router and get rental order information.

    Args:
        unit: Unit identifier

    Returns:
        SecurityProfileResponse with exists flag and rental order information if profile exists

    Raises:
        HTTPException: If router check fails
    """
    try:
        result = db.does_rental_exist(unit)

        # Handle error cases
        if isinstance(result, str) and "Error" in result:
            raise HTTPException(status_code=400, detail=result)

        # Handle the case where result is not a tuple (backward compatibility)
        if not isinstance(result, tuple):
            return SecurityProfileResponse(exists=bool(result), rental_order_info=None)

        # Extract tuple values
        profile_exists, rental_order_info = result

        # Convert rental_order_info to the expected format if it exists
        formatted_rental_info = None
        if profile_exists and rental_order_info:
            # rental_order_info should be a list of dictionaries from get_order_info_for_active_network
            if isinstance(rental_order_info, list) and len(rental_order_info) > 0:
                formatted_rental_info = [
                    RentalOrderInfo(**order_dict) for order_dict in rental_order_info
                ]

        return SecurityProfileResponse(
            exists=profile_exists,
            rental_order_info=formatted_rental_info
        )

    except Exception as e:
        logger.error(f"Error checking security profile for unit {unit}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check security profile: {str(e)}")

@app.post("/internal/db/save",
    response_model=Union[int, Dict[str, Any]],
    tags=["Internal"],
    summary="Execute save query",
    description="Internal endpoint to execute a save (INSERT/UPDATE/DELETE) query on the database")
async def save_query(request: SaveQueryRequest) -> Union[int, Dict[str, Any]]:
    """
    Execute a save query on the database.

    Args:
        request: SaveQueryRequest containing query and parameters

    Returns:
        Either lastrowid or rowcount depending on return_new_id flag

    Raises:
        HTTPException: If query execution fails
    """
    try:
        result = db.save_query(
            request.query,
            *((request.params,) if request.params else ()),
            returnNewIDInsteadOfRowCount=request.return_new_id
        )
        return result
    except Exception as e:
        logger.error(f"Database save query failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Database operation failed: {str(e)}"
        )

@app.post("/ping-hosts",
    tags=["Utility"],
    summary="Ping multiple hosts",
    description="Pings a list of IP addresses and returns status with response times")
async def ping_hosts(ip_list: List[str] = Body(..., description="List of IP addresses to ping")):
    """
    Ping multiple hosts and return their status.

    Args:
        ip_list: List of IP addresses to ping

    Returns:
        Dict mapping IP addresses to HTML status fragments
    """
    import socket
    import time

    results = {}
    for ip in ip_list:
        try:
            # Create a socket object
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)  # 1 second timeout

            # Record start time
            start_time = time.time()

            # Try to connect to port 80 (HTTP)
            result = sock.connect_ex((ip, 80))

            # Calculate elapsed time
            elapsed_time = (time.time() - start_time) * 1000  # Convert to ms

            # Close the socket
            sock.close()

            if result == 0:
                # Connection successful
                html = f'<span style="color: green;">{elapsed_time:.2f}ms</span>'
            else:
                # Try port 443 (HTTPS) as fallback
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                start_time = time.time()
                result = sock.connect_ex((ip, 443))
                elapsed_time = (time.time() - start_time) * 1000
                sock.close()

                if result == 0:
                    html = f'<span style="color: green;">{elapsed_time:.2f}ms</span>'
                else:
                    html = '<span style="color: red;">Connection failed</span>'
        except socket.gaierror:
            html = '<span style="color: red;">DNS resolution failed</span>'
        except socket.timeout:
            html = '<span style="color: red;">Connection timeout</span>'
        except Exception as e:
            html = f'<span style="color: red;">Error: {str(e)}</span>'

        results[ip] = html

    return results

@app.get("/unit/{unit}/router-profile",
    tags=["Units"],
    summary="Get Router Profile Information",
    description="Retrieves the router profile information for a specific unit",
    responses={
        200: {"description": "Successfully retrieved router profile"},
        404: {"description": "Unit not found"},
        500: {"description": "Internal server error"}
    })
async def get_router_profile_information(
    unit: str = Path(..., description="Unit identifier (without 'et' prefix)")
):
    """
    Get router profile information for a specific unit.

    Args:
        unit: Unit identifier (without 'et' prefix)

    Returns:
        Router profile information

    Raises:
        HTTPException: If retrieval fails or unit not found
    """
    try:
        profile_info = db.get_router_profile_information(unit)
        if not profile_info:
            raise HTTPException(
                status_code=404,
                detail=f"No router profile found for unit {unit}"
            )
        return profile_info
    except Exception as e:
        logger.error(f"Error getting router profile for unit {unit}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve router profile: {str(e)}"
        )

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="Gandalf API",
        version="1.0.0",
        description="API for managing network rentals and router configurations",
        routes=app.routes,
    )
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

 